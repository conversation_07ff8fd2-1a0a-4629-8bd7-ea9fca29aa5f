<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Chrome2Feishu 设置</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    .container {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #3370ff;
      margin-top: 0;
    }
    .section {
      margin-bottom: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }
    .section:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }
    .input-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"] {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    button {
      background-color: #3370ff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }
    button:hover {
      background-color: #2860e1;
    }
    .status {
      margin-top: 15px;
      padding: 10px;
      border-radius: 4px;
      display: none;
    }
    .success {
      background-color: #e6f7e6;
      color: #2e7d32;
      display: block;
    }
    .error {
      background-color: #fdecea;
      color: #c62828;
      display: block;
    }
    .help-text {
      font-size: 13px;
      color: #666;
      margin-top: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Chrome2Feishu 设置</h1>
    
    <div class="section">
      <h2>工作流配置</h2>
      
      <div class="input-group">
        <label for="workflowUrl">Coze工作流URL:</label>
        <input type="text" id="workflowUrl" placeholder="例如：https://www.coze.cn/open/playground/workflow_run">
        <p class="help-text">输入您的Coze工作流URL，用于接收网页数据并同步到飞书表格。</p>
      </div>
      
      <div class="input-group">
        <label for="customParam">自定义参数名称:</label>
        <input type="text" id="customParam" placeholder="url">
        <p class="help-text">指定传递给工作流的URL参数名称（默认为"url"）。</p>
      </div>
      
      <div class="input-group">
        <label for="accessToken">个人访问令牌 (PAT):</label>
        <input type="text" id="accessToken" placeholder="pat_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx">
        <p class="help-text">输入您的Coze个人访问令牌，用于授权访问工作流。</p>
      </div>
    </div>
    
    <div class="section">
      <h2>通知设置</h2>
      
      <div class="input-group">
        <label>
          <input type="checkbox" id="showNotifications">
          显示同步结果通知
        </label>
      </div>
    </div>
    
    <button id="saveButton">保存设置</button>
    
    <div id="status" class="status"></div>
  </div>
  
  <script src="options.js"></script>
</body>
</html> 