// 监听安装事件
chrome.runtime.onInstalled.addListener(function(details) {
  if (details.reason === 'install') {
    // 首次安装时设置默认选项
    chrome.storage.sync.set({
      workflowUrl: 'https://www.coze.cn/work_flow?space_id=7478999422093803560&workflow_id=7514865767113556004',
      customParam: 'url',
      accessToken: 'pat_SdcuBanXtMkrcPa9mORpG9SIFE0GVI3mCcXe7sZm0oBmcsIJqKMqDKpX9k9TBv4m',
      showNotifications: true
    });
    
    // 打开选项页
    chrome.runtime.openOptionsPage();
  }
});

// 监听来自popup的消息
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
  if (message.action === 'syncComplete' && message.success) {
    // 检查是否需要显示通知
    chrome.storage.sync.get('showNotifications', function(data) {
      if (data.showNotifications) {
        // 显示通知
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'images/icon128.png',
          title: 'Chrome2Feishu',
          message: '成功将网页内容同步到飞书表格！',
          priority: 2
        });
      }
    });
  }
}); 