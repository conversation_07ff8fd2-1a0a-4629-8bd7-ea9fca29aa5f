<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Chrome2Feishu</title>
  <style>
    body {
      width: 300px;
      padding: 15px;
      font-family: Arial, sans-serif;
    }
    .container {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    h1 {
      font-size: 18px;
      margin-top: 0;
      color: #3370ff;
    }
    .input-group {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
    label {
      font-size: 14px;
      font-weight: bold;
    }
    input {
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    button {
      background-color: #3370ff;
      color: white;
      border: none;
      padding: 10px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }
    button:hover {
      background-color: #2860e1;
    }
    .status {
      margin-top: 10px;
      padding: 10px;
      border-radius: 4px;
      display: none;
    }
    .success {
      background-color: #e6f7e6;
      color: #2e7d32;
      display: block;
    }
    .error {
      background-color: #fdecea;
      color: #c62828;
      display: block;
    }
    .info {
      background-color: #e3f2fd;
      color: #1565c0;
      display: block;
    }
    .settings {
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;
    }
    .settings a {
      color: #3370ff;
      text-decoration: none;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Chrome2Feishu</h1>
    
    <div class="input-group">
      <label for="workflowUrl">工作流URL:</label>
      <input type="text" id="workflowUrl" placeholder="请输入Coze工作流URL">
    </div>
    
    <button id="syncButton">同步到飞书</button>
    
    <div id="status" class="status"></div>
    
    <div class="settings">
      <a href="#" id="openOptions">设置</a>
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html> 