document.addEventListener('DOMContentLoaded', function() {
  // 获取DOM元素
  const workflowUrlInput = document.getElementById('workflowUrl');
  const syncButton = document.getElementById('syncButton');
  const statusDiv = document.getElementById('status');
  const openOptionsLink = document.getElementById('openOptions');
  
  // 从存储中加载工作流URL
  chrome.storage.sync.get('workflowUrl', function(data) {
    if (data.workflowUrl) {
      workflowUrlInput.value = data.workflowUrl;
    }
  });
  
  // 保存工作流URL到存储
  workflowUrlInput.addEventListener('change', function() {
    chrome.storage.sync.set({ 'workflowUrl': workflowUrlInput.value });
  });
  
  // 打开选项页面
  openOptionsLink.addEventListener('click', function() {
    chrome.runtime.openOptionsPage();
  });
  
  // 同步到飞书按钮点击事件
  syncButton.addEventListener('click', function() {
    const workflowUrl = workflowUrlInput.value.trim();
    
    if (!workflowUrl) {
      showStatus('请输入有效的工作流URL', 'error');
      return;
    }
    
    // 保存工作流URL
    chrome.storage.sync.set({ 'workflowUrl': workflowUrl });
    
    // 获取当前标签页URL
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      const currentUrl = tabs[0].url;
      
      // 调用工作流API
      callWorkflow(workflowUrl, currentUrl);
    });
  });
  
  // 调用工作流API函数
  function callWorkflow(workflowUrl, pageUrl) {
    // 显示加载状态
    syncButton.disabled = true;
    syncButton.textContent = '同步中...';

    // 获取自定义参数名称和访问令牌
    chrome.storage.sync.get({
      customParam: 'url',
      accessToken: ''
    }, function(data) {
      const paramName = data.customParam || 'url';
      const accessToken = data.accessToken;

      // 构建Coze工作流API调用
      const apiUrl = 'https://api.coze.cn/open_api/v2/chat';

      // 准备请求数据
      const requestData = {
        conversation_id: Date.now().toString(),
        bot_id: '7514865767113556004', // 从您的工作流ID提取
        user: 'chrome_extension_user',
        query: pageUrl,
        auto_save_history: true,
        additional_messages: []
      };

      // 准备请求选项
      const fetchOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      };

      // 如果有访问令牌，添加到请求头
      if (accessToken) {
        fetchOptions.headers['Authorization'] = `Bearer ${accessToken}`;
      }

      // 发送请求到工作流
      fetch(apiUrl, fetchOptions)
        .then(response => {
          if (!response.ok) {
            // 如果API调用失败，尝试原来的GET方式
            return fallbackToGetMethod(workflowUrl, pageUrl, paramName, accessToken);
          }
          return response.json();
        })
        .then(() => {
          // 调用成功
          showStatus('成功同步到飞书！', 'success');

          // 发送消息到background.js显示通知
          chrome.runtime.sendMessage({
            action: 'syncComplete',
            success: true
          });
        })
        .catch(error => {
          console.error('工作流调用错误:', error);
          // 调用失败，尝试备用方法
          fallbackToGetMethod(workflowUrl, pageUrl, paramName, accessToken);
        })
        .finally(() => {
          // 恢复按钮状态
          syncButton.disabled = false;
          syncButton.textContent = '同步到飞书';
        });
    });
  }

  // 备用的GET方法调用
  function fallbackToGetMethod(workflowUrl, pageUrl, paramName, accessToken) {
    // 如果工作流URL已经包含参数，使用&连接，否则使用?
    const separator = workflowUrl.includes('?') ? '&' : '?';
    const finalUrl = `${workflowUrl}${separator}${paramName}=${encodeURIComponent(pageUrl)}`;

    // 准备请求选项
    const fetchOptions = {
      method: 'GET',
      headers: {}
    };

    // 如果有访问令牌，添加到请求头
    if (accessToken) {
      fetchOptions.headers['Authorization'] = `Bearer ${accessToken}`;
    }

    return fetch(finalUrl, fetchOptions)
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.text(); // 改为text()以处理非JSON响应
      })
      .then(() => {
        // 调用成功
        showStatus('成功同步到飞书！', 'success');

        // 发送消息到background.js显示通知
        chrome.runtime.sendMessage({
          action: 'syncComplete',
          success: true
        });
      })
      .catch(error => {
        console.error('备用方法调用错误:', error);
        showStatus('同步失败：' + error.message, 'error');
        throw error;
      });
  }
  
  // 显示状态信息
  function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = 'status';
    statusDiv.classList.add(type);
    
    // 5秒后自动隐藏
    setTimeout(() => {
      statusDiv.style.display = 'none';
    }, 5000);
  }
}); 