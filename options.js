document.addEventListener('DOMContentLoaded', function() {
  // 获取DOM元素
  const workflowUrlInput = document.getElementById('workflowUrl');
  const customParamInput = document.getElementById('customParam');
  const accessTokenInput = document.getElementById('accessToken');
  const showNotificationsCheckbox = document.getElementById('showNotifications');
  const saveButton = document.getElementById('saveButton');
  const statusDiv = document.getElementById('status');
  
  // 加载已保存的设置
  loadOptions();
  
  // 保存按钮点击事件
  saveButton.addEventListener('click', saveOptions);
  
  // 加载选项函数
  function loadOptions() {
    chrome.storage.sync.get({
      workflowUrl: '',
      customParam: 'url',
      accessToken: '',
      showNotifications: true
    }, function(items) {
      workflowUrlInput.value = items.workflowUrl;
      customParamInput.value = items.customParam;
      accessTokenInput.value = items.accessToken;
      showNotificationsCheckbox.checked = items.showNotifications;
    });
  }
  
  // 保存选项函数
  function saveOptions() {
    const workflowUrl = workflowUrlInput.value.trim();
    const customParam = customParamInput.value.trim() || 'url';
    const accessToken = accessTokenInput.value.trim();
    const showNotifications = showNotificationsCheckbox.checked;
    
    // 验证工作流URL
    if (!workflowUrl) {
      showStatus('请输入有效的工作流URL', 'error');
      return;
    }
    
    // 保存设置
    chrome.storage.sync.set({
      workflowUrl: workflowUrl,
      customParam: customParam,
      accessToken: accessToken,
      showNotifications: showNotifications
    }, function() {
      showStatus('设置已保存', 'success');
    });
  }
  
  // 显示状态信息
  function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = 'status';
    statusDiv.classList.add(type);
    
    // 3秒后自动隐藏
    setTimeout(function() {
      statusDiv.style.display = 'none';
    }, 3000);
  }
}); 